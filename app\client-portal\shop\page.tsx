"use client"

import { useState, use<PERSON>ffe<PERSON>, use<PERSON><PERSON><PERSON>, use<PERSON><PERSON>back } from "react"
import Link from "next/link"
import Image from "next/image"
import { useProducts } from "@/lib/product-provider"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Slider } from "@/components/ui/slider"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { ClientPortalLayout } from "@/components/client-portal/client-portal-layout"
import { CurrencyDisplay } from "@/components/ui/currency-display"
import {
  Search,
  ShoppingBag,
  Heart,
  Star,
  Filter,
  ChevronDown,
  SlidersHorizontal,
  Loader2,
  AlertCircle
} from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import {
  Sheet,
  She<PERSON><PERSON>ontent,
  She<PERSON>Des<PERSON>,
  She<PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet"
import { Product } from "@/lib/products-data"

// Import beauty products from our shared data store
import { beautyProducts } from "@/lib/products-data"
import { debugRetailProducts, checkProductVisibility, fixProductRetailStatus, listAllProducts, debugEverything } from "@/lib/debug-products"

export default function ShopPage() {
  const { toast } = useToast()
  const { getRetailProducts, lastUpdated } = useProducts()
  const [products, setProducts] = useState<Product[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategories, setSelectedCategories] = useState<string[]>([])
  const [selectedTypes, setSelectedTypes] = useState<string[]>([])
  const [sortBy, setSortBy] = useState("featured")
  const [isMobileFilterOpen, setIsMobileFilterOpen] = useState(false)

  // Calculate dynamic price range based on products
  const maxPrice = useMemo(() => {
    if (products.length === 0) return 100
    return Math.ceil(Math.max(...products.map(p => p.salePrice || p.price)) / 10) * 10
  }, [products])

  const [priceRange, setPriceRange] = useState([0, 50])

  // Update price range when maxPrice changes
  useEffect(() => {
    setPriceRange([0, Math.min(50, maxPrice)])
  }, [maxPrice])

  // Set document title for SEO and add debug functions to global scope
  useEffect(() => {
    document.title = "Shop Products - Vanity Hub | Professional Beauty Products"

    // Add debug functions to global scope for easy access
    if (typeof window !== 'undefined') {
      (window as any).debugRetailProducts = debugRetailProducts
      (window as any).checkProductVisibility = checkProductVisibility
      (window as any).fixProductRetailStatus = fixProductRetailStatus
      (window as any).listAllProducts = listAllProducts
      if (debugEverything) {
        (window as any).debugEverything = debugEverything
      }
    }
  }, [])

  // Load retail products from the product provider
  useEffect(() => {
    try {
      setIsLoading(true)
      setError(null)
      const retailProducts = getRetailProducts()
      setProducts(retailProducts)

      // Debug logging
      console.log("🛒 Shop products refreshed:", retailProducts.length, "products loaded")
      console.log("📦 All retail products:", retailProducts)

      // Run debug functions
      debugRetailProducts()

      // Check specific products if they exist
      if (typeof window !== 'undefined') {
        setTimeout(() => {
          checkProductVisibility("Human Hair")
          checkProductVisibility("Nail Polish")
        }, 100)
      }

    } catch (err) {
      console.error("Error loading products:", err)
      setError("Failed to load products. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }, [getRetailProducts, lastUpdated])

  // Get unique categories and types for filters (memoized for performance)
  const categories = useMemo(() =>
    [...new Set(products.map(p => p.category))].filter(Boolean).sort(),
    [products]
  )

  const types = useMemo(() =>
    [...new Set(products.map(p => p.type))].filter(Boolean).sort(),
    [products]
  )

  // Filter and sort products (memoized for performance)
  const filteredAndSortedProducts = useMemo(() => {
    // Filter products
    const filtered = products.filter(product => {
      // Search filter
      if (searchQuery) {
        const query = searchQuery.toLowerCase()
        const searchableText = `${product.name} ${product.description} ${product.category} ${product.type}`.toLowerCase()
        if (!searchableText.includes(query)) {
          return false
        }
      }

      // Price filter
      const productPrice = product.salePrice || product.price
      if (productPrice < priceRange[0] || productPrice > priceRange[1]) {
        return false
      }

      // Category filter
      if (selectedCategories.length > 0 && !selectedCategories.includes(product.category.toLowerCase())) {
        return false
      }

      // Type filter
      if (selectedTypes.length > 0 && !selectedTypes.includes(product.type.toLowerCase())) {
        return false
      }

      return true
    })

    // Sort products
    return [...filtered].sort((a, b) => {
      switch (sortBy) {
        case "price-low-high":
          return (a.salePrice || a.price) - (b.salePrice || b.price)
        case "price-high-low":
          return (b.salePrice || b.price) - (a.salePrice || a.price)
        case "rating":
          return (b.rating || 0) - (a.rating || 0)
        case "newest":
          return a.isNew ? -1 : b.isNew ? 1 : 0
        default: // featured
          return (b.isBestSeller ? 1 : 0) - (a.isBestSeller ? 1 : 0)
      }
    })
  }, [products, searchQuery, priceRange, selectedCategories, selectedTypes, sortBy])

  // Memoized event handlers for better performance
  const handleCategoryToggle = useCallback((category: string) => {
    setSelectedCategories(prev =>
      prev.includes(category)
        ? prev.filter(c => c !== category)
        : [...prev, category]
    )
  }, [])

  const handleTypeToggle = useCallback((type: string) => {
    setSelectedTypes(prev =>
      prev.includes(type)
        ? prev.filter(t => t !== type)
        : [...prev, type]
    )
  }, [])

  const clearFilters = useCallback(() => {
    setSearchQuery("")
    setPriceRange([0, Math.min(50, maxPrice)])
    setSelectedCategories([])
    setSelectedTypes([])
  }, [maxPrice])

  const handleAddToCart = useCallback(async (product: Product) => {
    // Check if we have enough stock
    if (product.stock <= 0) {
      toast({
        title: "Out of stock",
        description: `Sorry, ${product.name} is currently out of stock.`,
        variant: "destructive"
      });
      return;
    }

    try {
      // Add to cart - this would update a cart state and potentially call an API
      toast({
        title: "Added to cart",
        description: `${product.name} has been added to your cart.`,
      });

      // Calculate loyalty points (5 points per $1 spent on products)
      const pointsEarned = Math.floor((product.salePrice || product.price) * 5);

      // Get client ID from localStorage (with error handling)
      let clientId: string;
      try {
        clientId = localStorage.getItem("client_id") || "client123";
      } catch (err) {
        console.warn("localStorage not available:", err);
        clientId = "client123";
      }

      // Add loyalty points with proper error handling
      const response = await fetch('/api/client-portal/loyalty', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          clientId,
          points: pointsEarned,
          description: `Product Purchase: ${product.name}`
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      // Show a toast with the points earned
      toast({
        title: "Loyalty Points Earned!",
        description: `You earned ${pointsEarned} loyalty points for this purchase.`,
        variant: "default",
      });

      // If the client reached a new tier, show a special toast
      if (data.tierUpdated) {
        toast({
          title: "Tier Upgraded!",
          description: `Congratulations! You've reached ${data.newTier} tier.`,
          variant: "default",
        });
      }
    } catch (error) {
      console.error("Error processing purchase:", error);
      toast({
        title: "Error",
        description: "There was an issue processing your purchase. Please try again.",
        variant: "destructive",
      });
    }
  }, [toast])

  const handleAddToWishlist = useCallback((product: Product) => {
    // In a real app, we would store this in a user's wishlist
    toast({
      title: "Added to wishlist",
      description: `${product.name} has been added to your wishlist.`,
    });
  }, [toast])

  // Error boundary component for images
  const ProductImage = ({ product }: { product: Product }) => {
    const [imageError, setImageError] = useState(false)

    return (
      <Image
        src={imageError ? "/placeholder.jpg" : product.image}
        alt={product.name}
        fill
        className="object-cover group-hover:scale-105 transition-transform duration-300"
        sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
        onError={() => setImageError(true)}
        priority={false}
      />
    )
  }

  // Show error state
  if (error) {
    return (
      <div className="container mx-auto px-4 py-16 text-center">
        <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
        <h1 className="text-2xl font-bold mb-4">Error Loading Products</h1>
        <p className="text-gray-600 mb-8">{error}</p>
        <Button onClick={() => window.location.reload()}>
          Try Again
        </Button>
      </div>
    )
  }

  return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
          <div>
            <h1 className="text-2xl font-bold mb-1">Shop Products</h1>
            <p className="text-gray-600">Browse our collection of professional beauty products</p>
          </div>
          <div className="flex items-center gap-3">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" aria-hidden="true" />
              <Input
                placeholder="Search products..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 w-full md:w-[250px]"
                aria-label="Search products"
              />
            </div>

            <Sheet open={isMobileFilterOpen} onOpenChange={setIsMobileFilterOpen}>
              <SheetTrigger asChild>
                <Button variant="outline" size="icon" className="md:hidden">
                  <Filter className="h-4 w-4" />
                </Button>
              </SheetTrigger>
              <SheetContent side="left" className="w-[300px] sm:w-[350px]">
                <SheetHeader className="mb-6">
                  <SheetTitle>Filters</SheetTitle>
                  <SheetDescription>
                    Refine your product search
                  </SheetDescription>
                </SheetHeader>

                <div className="space-y-6">
                  <div>
                    <h3 className="font-medium mb-3">Price Range</h3>
                    <div className="px-2">
                      <Slider
                        value={priceRange}
                        min={0}
                        max={maxPrice}
                        step={1}
                        onValueChange={setPriceRange}
                        aria-label="Price range filter"
                      />
                      <div className="flex justify-between mt-2 text-sm">
                        <span><CurrencyDisplay amount={priceRange[0]} /></span>
                        <span><CurrencyDisplay amount={priceRange[1]} /></span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="font-medium mb-3">Categories</h3>
                    <div className="space-y-2">
                      {categories.map((category) => (
                        <div key={category} className="flex items-center space-x-2">
                          <Checkbox
                            id={`category-${category}-mobile`}
                            checked={selectedCategories.includes(category.toLowerCase())}
                            onCheckedChange={() => handleCategoryToggle(category.toLowerCase())}
                          />
                          <Label htmlFor={`category-${category}-mobile`} className="capitalize">
                            {category}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h3 className="font-medium mb-3">Product Type</h3>
                    <div className="space-y-2">
                      {types.map((type) => (
                        <div key={type} className="flex items-center space-x-2">
                          <Checkbox
                            id={`type-${type}-mobile`}
                            checked={selectedTypes.includes(type.toLowerCase())}
                            onCheckedChange={() => handleTypeToggle(type.toLowerCase())}
                          />
                          <Label htmlFor={`type-${type}-mobile`} className="capitalize">
                            {type}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>

                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={clearFilters}
                  >
                    Clear All Filters
                  </Button>

                  <Button
                    className="w-full bg-pink-600 hover:bg-pink-700"
                    onClick={() => setIsMobileFilterOpen(false)}
                  >
                    Apply Filters
                  </Button>
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>

        <div className="flex flex-col md:flex-row gap-8">
          {/* Desktop Filters */}
          <div className="hidden md:block w-64 flex-shrink-0">
            <div className="sticky top-24 space-y-6">
              <div>
                <div className="flex items-center justify-between mb-3">
                  <h3 className="font-medium">Filters</h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 px-2 text-pink-600"
                    onClick={clearFilters}
                  >
                    Clear All
                  </Button>
                </div>

                <div className="space-y-4">
                  <div>
                    <h4 className="text-sm font-medium mb-3 flex items-center">
                      Price Range
                      <ChevronDown className="ml-auto h-4 w-4" />
                    </h4>
                    <div className="px-2">
                      <Slider
                        value={priceRange}
                        min={0}
                        max={maxPrice}
                        step={1}
                        onValueChange={setPriceRange}
                        aria-label="Price range filter"
                      />
                      <div className="flex justify-between mt-2 text-sm">
                        <span><CurrencyDisplay amount={priceRange[0]} /></span>
                        <span><CurrencyDisplay amount={priceRange[1]} /></span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium mb-3 flex items-center">
                      Categories
                      <ChevronDown className="ml-auto h-4 w-4" />
                    </h4>
                    <div className="space-y-2">
                      {categories.map((category) => (
                        <div key={category} className="flex items-center space-x-2">
                          <Checkbox
                            id={`category-${category}`}
                            checked={selectedCategories.includes(category.toLowerCase())}
                            onCheckedChange={() => handleCategoryToggle(category.toLowerCase())}
                          />
                          <Label htmlFor={`category-${category}`} className="capitalize">
                            {category}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium mb-3 flex items-center">
                      Product Type
                      <ChevronDown className="ml-auto h-4 w-4" />
                    </h4>
                    <div className="space-y-2">
                      {types.map((type) => (
                        <div key={type} className="flex items-center space-x-2">
                          <Checkbox
                            id={`type-${type}`}
                            checked={selectedTypes.includes(type.toLowerCase())}
                            onCheckedChange={() => handleTypeToggle(type.toLowerCase())}
                          />
                          <Label htmlFor={`type-${type}`} className="capitalize">
                            {type}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Product Grid */}
          <div className="flex-1">
            <div className="flex justify-between items-center mb-6">
              <p className="text-sm text-gray-500" role="status" aria-live="polite">
                Showing {filteredAndSortedProducts.length} of {products.length} products
              </p>

              <div className="flex items-center gap-2">
                <SlidersHorizontal className="h-4 w-4 text-gray-500" aria-hidden="true" />
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="text-sm border-none bg-transparent focus:ring-0"
                  aria-label="Sort products by"
                >
                  <option value="featured">Featured</option>
                  <option value="price-low-high">Price: Low to High</option>
                  <option value="price-high-low">Price: High to Low</option>
                  <option value="rating">Top Rated</option>
                  <option value="newest">Newest</option>
                </select>
              </div>
            </div>

            {isLoading ? (
              <div className="flex justify-center items-center py-20" role="status" aria-label="Loading products">
                <Loader2 className="h-8 w-8 text-pink-600 animate-spin" />
                <span className="ml-2 text-lg text-gray-600">Loading products...</span>
              </div>
            ) : filteredAndSortedProducts.length === 0 ? (
              <div className="text-center py-12 bg-gray-50 rounded-lg">
                <ShoppingBag className="h-12 w-12 text-gray-300 mx-auto mb-4" aria-hidden="true" />
                <h3 className="text-lg font-medium mb-2">No products found</h3>
                <p className="text-gray-500 mb-6 max-w-md mx-auto">
                  We couldn't find any products matching your criteria. Try adjusting your filters or search query.
                </p>
                <Button variant="outline" onClick={clearFilters}>
                  Clear Filters
                </Button>
              </div>
            ) : (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6" role="grid" aria-label="Product grid">
                {filteredAndSortedProducts.map((product) => (
                  <Card key={product.id} className="overflow-hidden group">
                    <CardContent className="p-0">
                      <div className="relative h-64 overflow-hidden">
                        <ProductImage product={product} />
                        <div className="absolute top-3 left-3 flex flex-col gap-2">
                          {product.isNew && (
                            <Badge className="bg-pink-600">New</Badge>
                          )}
                          {product.isBestSeller && (
                            <Badge className="bg-amber-500">Best Seller</Badge>
                          )}
                          {product.isSale && (
                            <Badge className="bg-red-500">Sale</Badge>
                          )}
                        </div>
                        <div className="absolute top-3 right-3">
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8 rounded-full bg-white/80 hover:bg-white text-gray-600 hover:text-pink-600"
                            onClick={() => handleAddToWishlist(product)}
                            aria-label={`Add ${product.name} to wishlist`}
                          >
                            <Heart className="h-4 w-4" />
                          </Button>
                        </div>
                        <div className="absolute inset-x-0 bottom-0 h-12 bg-gradient-to-t from-black/60 to-transparent" />
                      </div>

                      <div className="p-4">
                        <div className="flex items-center gap-1 mb-1">
                          <div className="flex text-amber-400">
                            {[...Array(5)].map((_, i) => (
                              <Star
                                key={i}
                                className={`h-3.5 w-3.5 ${i < Math.floor(product.rating || 0) ? "fill-amber-400" : "fill-gray-200"}`}
                              />
                            ))}
                          </div>
                          <span className="text-xs text-gray-500">({product.reviewCount || 0})</span>
                        </div>

                        <Link href={`/client-portal/shop/${product.id}`} className="block">
                          <h3 className="font-medium mb-1 group-hover:text-pink-600 transition-colors">
                            {product.name}
                          </h3>
                        </Link>

                        <p className="text-sm text-gray-500 mb-3 line-clamp-2">
                          {product.description}
                        </p>

                        <div className="flex justify-between items-center">
                          <div>
                            {product.isSale && product.salePrice ? (
                              <div className="flex items-center gap-2">
                                <span className="font-bold"><CurrencyDisplay amount={product.salePrice || 0} /></span>
                                <span className="text-sm text-gray-500 line-through"><CurrencyDisplay amount={product.price} /></span>
                              </div>
                            ) : (
                              <span className="font-bold"><CurrencyDisplay amount={product.price} /></span>
                            )}
                          </div>

                          <Button
                            size="sm"
                            className="bg-pink-600 hover:bg-pink-700"
                            onClick={() => handleAddToCart(product)}
                            aria-label={`Add ${product.name} to cart`}
                            disabled={product.stock <= 0}
                          >
                            <ShoppingBag className="h-4 w-4 mr-1" />
                            {product.stock <= 0 ? "Out of Stock" : "Add"}
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
  )
}
