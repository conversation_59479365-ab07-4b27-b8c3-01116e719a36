// Debug utilities for product and category management

export const debugProductData = () => {
  if (typeof window === 'undefined') return

  console.group('🔍 Product Data Debug')

  try {
    // Check localStorage data
    const storedProducts = localStorage.getItem('salon_products')
    const storedCategories = localStorage.getItem('salon_categories')
    const storedTypes = localStorage.getItem('salon_product_types')

    console.log('📦 Stored Products:', storedProducts ? JSON.parse(storedProducts).length : 0)
    console.log('📂 Stored Categories:', storedCategories ? JSON.parse(storedCategories) : [])
    console.log('🏷️ Stored Types:', storedTypes ? JSON.parse(storedTypes).length : 0)

    // Check for Extensions category specifically
    if (storedCategories) {
      const categories = JSON.parse(storedCategories)
      const extensionsCategory = categories.find((c: any) =>
        c.name && typeof c.name === 'string' && c.name.toLowerCase().includes('extension')
      )
      console.log('🔗 Extensions Category:', extensionsCategory)
    }

    // Check for products in Extensions category
    if (storedProducts) {
      const products = JSON.parse(storedProducts)
      const extensionProducts = products.filter((p: any) =>
        p.category && typeof p.category === 'string' && p.category.toLowerCase().includes('extension')
      )
      console.log('🛍️ Extension Products:', extensionProducts)
    }
  } catch (error) {
    console.error('❌ Error debugging product data:', error)
  }

  console.groupEnd()
}

// Comprehensive debug function to check everything
export const debugEverything = () => {
  console.group('🔍 COMPLETE SYSTEM DEBUG')

  debugProductData()
  debugRetailProducts()
  listAllProducts()

  console.groupEnd()
}

export const debugCategorySync = (oldName: string, newName: string) => {
  console.group(`🔄 Category Sync: "${oldName}" → "${newName}"`)

  // Check products before and after
  const storedProducts = localStorage.getItem('salon_products')
  if (storedProducts) {
    const products = JSON.parse(storedProducts)
    const affectedProducts = products.filter((p: any) =>
      p.category === oldName || p.category === newName
    )
    console.log('📦 Affected Products:', affectedProducts)
  }

  console.groupEnd()
}

export const debugRetailProducts = () => {
  console.group('🛒 Retail Products Debug')

  try {
    const storedProducts = localStorage.getItem('salon_products')
    if (storedProducts) {
      const products = JSON.parse(storedProducts)
      const retailProducts = products.filter((p: any) => p.isRetail && p.isActive !== false)
      const extensionRetailProducts = retailProducts.filter((p: any) =>
        p.category && typeof p.category === 'string' && p.category.toLowerCase().includes('extension')
      )

      console.log('🛍️ Total Retail Products:', retailProducts.length)
      console.log('🔗 Extension Retail Products:', extensionRetailProducts)
      console.log('📊 Categories in Retail:', [...new Set(retailProducts.map((p: any) => p.category).filter(Boolean))])
    } else {
      console.log('❌ No stored products found')
    }
  } catch (error) {
    console.error('❌ Error debugging retail products:', error)
  }

  console.groupEnd()
}

// Helper to clear all product data (for testing)
export const clearProductData = () => {
  if (typeof window === 'undefined') return

  localStorage.removeItem('salon_products')
  localStorage.removeItem('salon_categories')
  localStorage.removeItem('salon_product_types')
  localStorage.removeItem('salon_product_transfers')

  console.log('🧹 All product data cleared')
}

// Helper to check if a product should appear in shop
export const checkProductVisibility = (productName?: string) => {
  if (!productName || typeof productName !== 'string') {
    console.log('❌ Please provide a product name as a string')
    return null
  }

  const storedProducts = localStorage.getItem('salon_products')
  if (!storedProducts) {
    console.log('❌ No stored products found')
    return null
  }

  const products = JSON.parse(storedProducts)
  const product = products.find((p: any) =>
    p.name && typeof p.name === 'string' &&
    p.name.toLowerCase().includes(productName.toLowerCase())
  )

  if (!product) {
    console.log(`❌ Product "${productName}" not found`)
    return null
  }

  console.group(`👁️ Visibility Check: "${product.name}"`)
  console.log('🏷️ Category:', product.category)
  console.log('🛒 Is Retail:', product.isRetail)
  console.log('✅ Is Active:', product.isActive)
  console.log('🔍 Should appear in shop:', product.isRetail && product.isActive !== false)
  console.groupEnd()

  return product
}

// Helper to fix products that should be retail but aren't
export const fixProductRetailStatus = (productName?: string | null) => {
  const storedProducts = localStorage.getItem('salon_products')
  if (!storedProducts) {
    console.log('❌ No stored products found')
    return
  }

  const products = JSON.parse(storedProducts)
  let updated = false

  const updatedProducts = products.map((p: any) => {
    // If specific product name provided, only fix that one
    if (productName && typeof productName === 'string' && p.name && typeof p.name === 'string' && !p.name.toLowerCase().includes(productName.toLowerCase())) {
      return p
    }

    // Fix products that should be retail
    if (p.isRetail === undefined || p.isRetail === null) {
      console.log(`🔧 Fixing retail status for: ${p.name || 'Unnamed Product'}`)
      updated = true
      return { ...p, isRetail: true, isActive: true }
    }

    return p
  })

  if (updated) {
    localStorage.setItem('salon_products', JSON.stringify(updatedProducts))
    console.log('✅ Products fixed! Please refresh the page.')
  } else {
    console.log('ℹ️ No products needed fixing')
  }
}

// Helper to list all products with their retail status
export const listAllProducts = () => {
  try {
    const storedProducts = localStorage.getItem('salon_products')
    if (!storedProducts) {
      console.log('❌ No stored products found')
      return
    }

    const products = JSON.parse(storedProducts)
    console.group('📋 All Products Status')

    if (products.length === 0) {
      console.log('ℹ️ No products in storage')
    } else {
      products.forEach((p: any, index: number) => {
        console.log(`${index + 1}. ${p.name || 'Unnamed Product'}`)
        console.log(`   Category: ${p.category || 'No Category'}`)
        console.log(`   Retail: ${p.isRetail}`)
        console.log(`   Active: ${p.isActive}`)
        console.log(`   Should show in shop: ${p.isRetail && p.isActive !== false}`)
        console.log('---')
      })
    }

    console.groupEnd()
  } catch (error) {
    console.error('❌ Error listing products:', error)
  }
}
