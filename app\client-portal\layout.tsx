"use client"

import Link from "next/link"
import { Button } from "@/components/ui/button"
import { User, UserPlus, <PERSON>u, LogOut } from "lucide-react"
import { useState, useEffect } from "react"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"
import { useToast } from "@/components/ui/use-toast"
import { useRouter, usePathname } from "next/navigation"

export default function ClientPortalRootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const { toast } = useToast();
  const router = useRouter();
  const pathname = usePathname();

  // Check if user is logged in
  useEffect(() => {
    const token = localStorage.getItem("client_auth_token");
    setIsLoggedIn(!!token);
  }, [pathname]);

  // Handle logout
  const handleLogout = () => {
    localStorage.removeItem("client_auth_token");
    localStorage.removeItem("client_email");
    localStorage.removeItem("client_id");

    setIsLoggedIn(false);

    // Dispatch custom event to notify other components of logout
    window.dispatchEvent(new CustomEvent('client-auth-changed', { detail: { isLoggedIn: false } }));

    toast({
      title: "Logged out",
      description: "You have been successfully logged out",
    });

    router.push("/client-portal");
  };

  return (
    <div className="flex min-h-screen flex-col">
      <header className="bg-white shadow-sm sticky top-0 z-10">
        <div className="container mx-auto px-4 py-3 flex justify-between items-center">
          <div className="flex items-center gap-2">
            <Link href="/client-portal" className="flex items-center gap-2">
              <div className="w-8 h-8 rounded-full bg-pink-500 flex items-center justify-center text-white font-bold text-lg">
                V
              </div>
              <h1 className="text-lg font-bold hidden sm:block">Vanity Hub</h1>
            </Link>
          </div>

          {/* Desktop buttons */}
          <div className="hidden sm:flex items-center gap-3">
            {isLoggedIn ? (
              <>
                <Button size="sm" variant="outline" asChild>
                  <Link href="/client-portal/dashboard">
                    <User className="mr-2 h-4 w-4" />
                    Dashboard
                  </Link>
                </Button>
                <Button size="sm" className="bg-pink-600 hover:bg-pink-700" onClick={handleLogout}>
                  <LogOut className="mr-2 h-4 w-4" />
                  Sign Out
                </Button>
              </>
            ) : (
              <>
                <Button size="sm" variant="outline" asChild>
                  <Link href="/client-portal#auth-section"
                    onClick={() => document.dispatchEvent(new CustomEvent('switch-auth-tab', { detail: 'login' }))}>
                    <User className="mr-2 h-4 w-4" />
                    Sign In
                  </Link>
                </Button>
                <Button size="sm" className="bg-pink-600 hover:bg-pink-700" asChild>
                  <Link href="/client-portal#auth-section"
                    onClick={() => document.dispatchEvent(new CustomEvent('switch-auth-tab', { detail: 'signup' }))}>
                    <UserPlus className="mr-2 h-4 w-4" />
                    Sign Up
                  </Link>
                </Button>
              </>
            )}
          </div>

          {/* Mobile menu button */}
          <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
            <SheetTrigger asChild>
              <Button variant="ghost" size="icon" className="sm:hidden">
                <Menu className="h-5 w-5" />
              </Button>
            </SheetTrigger>
            <SheetContent side="right" className="w-[300px]">
              <div className="flex flex-col h-full">
                <div className="flex items-center justify-between py-4 border-b">
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 rounded-full bg-pink-500 flex items-center justify-center text-white font-bold text-lg">
                      V
                    </div>
                    <h2 className="text-lg font-bold">Vanity Hub</h2>
                  </div>
                </div>
                <div className="flex flex-col gap-4 py-6">
                  {isLoggedIn ? (
                    <>
                      <Button variant="outline" asChild onClick={() => setIsMobileMenuOpen(false)}>
                        <Link href="/client-portal/dashboard">
                          <User className="mr-2 h-4 w-4" />
                          Dashboard
                        </Link>
                      </Button>
                      <Button
                        className="bg-pink-600 hover:bg-pink-700"
                        onClick={() => {
                          handleLogout();
                          setIsMobileMenuOpen(false);
                        }}
                      >
                        <LogOut className="mr-2 h-4 w-4" />
                        Sign Out
                      </Button>
                    </>
                  ) : (
                    <>
                      <Button variant="outline" asChild onClick={() => setIsMobileMenuOpen(false)}>
                        <Link href="/client-portal#auth-section"
                          onClick={() => document.dispatchEvent(new CustomEvent('switch-auth-tab', { detail: 'login' }))}>
                          <User className="mr-2 h-4 w-4" />
                          Sign In
                        </Link>
                      </Button>
                      <Button className="bg-pink-600 hover:bg-pink-700" asChild onClick={() => setIsMobileMenuOpen(false)}>
                        <Link href="/client-portal#auth-section"
                          onClick={() => document.dispatchEvent(new CustomEvent('switch-auth-tab', { detail: 'signup' }))}>
                          <UserPlus className="mr-2 h-4 w-4" />
                          Sign Up
                        </Link>
                      </Button>
                    </>
                  )}
                </div>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </header>
      <main className="flex-1">
        {children}
      </main>
      <footer className="bg-white border-t py-6">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 rounded-full bg-pink-500 flex items-center justify-center text-white font-bold text-lg">
                V
              </div>
              <h2 className="text-lg font-bold">Vanity Hub</h2>
            </div>
            <div className="text-center text-sm text-gray-500">
              <p>&copy; {new Date().getFullYear()} Vanity Hub. All rights reserved.</p>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
